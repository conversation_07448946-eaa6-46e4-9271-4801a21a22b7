package io.dataflint.spark

import org.apache.spark.SparkContext
import org.apache.spark.api.plugin.{DriverPlugin, ExecutorPlugin, PluginContext, SparkPlugin}
import org.apache.spark.dataflint.DataflintSparkUILoader
import org.apache.spark.internal.Logging

import java.util
import scala.collection.JavaConverters.mapAsJavaMapConverter

class SparkDataflintPlugin extends SparkPlugin {
  override def driverPlugin(): DriverPlugin = new SparkDataflintDriverPlugin()

  override def executorPlugin(): ExecutorPlugin = null
}

class SparkDataflintDriverPlugin extends DriverPlugin with Logging {
  var sc: SparkContext = null

  override def init(sc: SparkContext, pluginContext: PluginContext): util.Map[String, String] = {
    this.sc = sc
    Map[String, String]().asJava
  }

  override def registerMetrics(appId: String, pluginContext: PluginContext): Unit = {
    var webUrl = DataflintSparkUILoader.install(sc)
    logInfo(s"spark dataflint url is $webUrl/dataflint")
    super.registerMetrics(appId, pluginContext)
  }

  /**
   * Detect if running in YARN proxy environment and construct proper external URL
   */
  private def detectYarnProxyUrl(appId: String): Option[String] = {
    try {
      // Check for YARN application master web URL
      val amWebUrl = sc.getConf.getOption("spark.yarn.am.webUrl")
      val yarnWebProxyBase = sc.getConf.getOption("spark.yarn.webProxy.base")

      (amWebUrl, yarnWebProxyBase) match {
        case (Some(amUrl), _) =>
          // If AM web URL is available, construct dataflint URL based on it
          val baseUrl = if (amUrl.endsWith("/")) amUrl.dropRight(1) else amUrl
          Some(s"$baseUrl/dataflint")
        case (_, Some(proxyBase)) =>
          // If YARN web proxy base is available, use it
          val baseUrl = if (proxyBase.endsWith("/")) proxyBase.dropRight(1) else proxyBase
          Some(s"$baseUrl/dataflint")
        case _ =>
          // Check if we can detect EMR environment
          val sparkMaster = sc.getConf.get("spark.master", "")
          if (sparkMaster.startsWith("yarn")) {
            logInfo("Detected YARN environment but no proxy URL configuration found. " +
              "Consider setting spark.dataflint.webUrl to the correct external URL.")
          }
          None
      }
    } catch {
      case e: Exception =>
        logWarning(s"Failed to detect YARN proxy URL: ${e.getMessage}")
        None
    }
  }
}
