package io.dataflint.spark

import org.apache.spark.SparkContext
import org.apache.spark.api.plugin.{DriverPlugin, ExecutorPlugin, PluginContext, SparkPlugin}
import org.apache.spark.dataflint.DataflintSparkUILoader
import org.apache.spark.internal.Logging

import java.util
import scala.collection.JavaConverters.mapAsJavaMapConverter

class SparkDataflintPlugin extends SparkPlugin {
  override def driverPlugin(): DriverPlugin = new SparkDataflintDriverPlugin()

  override def executorPlugin(): ExecutorPlugin = null
}

class SparkDataflintDriverPlugin extends DriverPlugin with Logging {
  var sc: SparkContext = null

  override def init(sc: SparkContext, pluginContext: PluginContext): util.Map[String, String] = {
    this.sc = sc
    Map[String, String]().asJava
  }

  override def registerMetrics(appId: String, pluginContext: PluginContext): Unit = {
    var webUrl = DataflintSparkUILoader.install(sc)

    // Enhanced URL detection for different environments
    val dataflintUrl = detectDataflintUrl(webUrl, appId)
    logInfo(s"spark dataflint url is $dataflintUrl")
    super.registerMetrics(appId, pluginContext)
  }

  private def detectDataflintUrl(webUrl: String, appId: String): String = {
    try {
      // Check for YARN proxy environment variables and configurations
      val amWebUrl = sc.getConf.getOption("spark.org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter.param.PROXY_URI_BASES")
      val yarnWebProxyBase = sc.getConf.getOption("spark.yarn.historyServer.address")
        .orElse(sc.getConf.getOption("spark.yarn.proxy.base"))
        .orElse(Option(System.getenv("YARN_WEB_PROXY_BASE")))

      (amWebUrl, yarnWebProxyBase) match {
        case (Some(amUrl), _) =>
          // If AM web URL is available, construct dataflint URL based on it
          val baseUrl = if (amUrl.endsWith("/")) amUrl.dropRight(1) else amUrl
          s"$baseUrl/dataflint"
        case (_, Some(proxyBase)) =>
          // If YARN web proxy base is available, use it
          val baseUrl = if (proxyBase.endsWith("/")) proxyBase.dropRight(1) else proxyBase
          s"$baseUrl/dataflint"
        case _ =>
          // Check if we can detect EMR environment
          val sparkMaster = sc.getConf.get("spark.master", "")
          if (sparkMaster.startsWith("yarn")) {
            logInfo("Detected YARN environment but no proxy URL configuration found. " +
              "Consider setting spark.dataflint.webUrl to the correct external URL.")
          }
          s"$webUrl/dataflint"
      }
    } catch {
      case e: Exception =>
        logWarning(s"Failed to detect dataflint URL, using default: ${e.getMessage}")
        s"$webUrl/dataflint"
    }
  }
}
