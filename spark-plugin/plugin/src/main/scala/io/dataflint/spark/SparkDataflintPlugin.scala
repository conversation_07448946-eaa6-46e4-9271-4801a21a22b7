package io.dataflint.spark

import org.apache.spark.SparkContext
import org.apache.spark.api.plugin.{DriverPlugin, ExecutorPlugin, PluginContext, SparkPlugin}
import org.apache.spark.dataflint.DataflintSparkUILoader
import org.apache.spark.dataflint.config.ProxyEnvironmentDetector
import org.apache.spark.internal.Logging

import java.util
import scala.collection.JavaConverters.mapAsJavaMapConverter

class SparkDataflintPlugin extends SparkPlugin {
  override def driverPlugin(): DriverPlugin = new SparkDataflintDriverPlugin()

  override def executorPlugin(): ExecutorPlugin = null
}

class SparkDataflintDriverPlugin extends DriverPlugin with Logging {
  var sc: SparkContext = null

  override def init(sc: SparkContext, pluginContext: PluginContext): util.Map[String, String] = {
    this.sc = sc
    Map[String, String]().asJava
  }

  override def registerMetrics(appId: String, pluginContext: PluginContext): Unit = {
    var webUrl = DataflintSparkUILoader.install(sc)

    // Enhanced proxy environment detection for logging purposes
    try {
      val proxyConfig = ProxyEnvironmentDetector.detectEnvironment(sc)
      val dataflintUrl = ProxyEnvironmentDetector.getDataFlintUrl(sc, webUrl)

      logInfo(s"spark dataflint url is $dataflintUrl")
      logInfo(s"Proxy environment detected: ${proxyConfig.proxyType}, isProxy: ${proxyConfig.isProxyEnvironment}")
      if (proxyConfig.baseUrl.isDefined) {
        logInfo(s"Proxy base URL: ${proxyConfig.baseUrl.get}")
      }
    } catch {
      case e: Exception =>
        logWarning(s"Failed to detect proxy environment: ${e.getMessage}")
        logInfo(s"spark dataflint url is $webUrl/dataflint")
    }

    super.registerMetrics(appId, pluginContext)
  }
}
