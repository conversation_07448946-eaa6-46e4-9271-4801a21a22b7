package org.apache.spark.dataflint.debug

import org.apache.spark.SparkContext
import org.apache.spark.internal.Logging

import javax.servlet.http.HttpServletRequest

/**
 * Debug utilities for troubleshooting proxy environment issues
 */
object ProxyDebugUtils extends Logging {

  /**
   * Log comprehensive request information for debugging
   */
  def logRequestDetails(request: HttpServletRequest, context: String): Unit = {
    logInfo(s"=== $context Request Debug Info ===")
    logInfo(s"Request Method: ${request.getMethod}")
    logInfo(s"Request URI: ${request.getRequestURI}")
    logInfo(s"Request URL: ${request.getRequestURL}")
    logInfo(s"Context Path: ${request.getContextPath}")
    logInfo(s"Servlet Path: ${request.getServletPath}")
    logInfo(s"Path Info: ${request.getPathInfo}")
    logInfo(s"Query String: ${request.getQueryString}")
    logInfo(s"Remote Addr: ${request.getRemoteAddr}")
    logInfo(s"Remote Host: ${request.getRemoteHost}")
    logInfo(s"Remote Port: ${request.getRemotePort}")
    logInfo(s"Server Name: ${request.getServerName}")
    logInfo(s"Server Port: ${request.getServerPort}")
    logInfo(s"Scheme: ${request.getScheme}")
    
    // Log all headers
    logInfo("=== Request Headers ===")
    val headerNames = request.getHeaderNames()
    while (headerNames.hasMoreElements()) {
      val headerName = headerNames.nextElement()
      val headerValue = request.getHeader(headerName)
      logInfo(s"Header $headerName: $headerValue")
    }
    
    // Log all parameters
    logInfo("=== Request Parameters ===")
    val paramNames = request.getParameterNames()
    while (paramNames.hasMoreElements()) {
      val paramName = paramNames.nextElement()
      val paramValue = request.getParameter(paramName)
      logInfo(s"Parameter $paramName: $paramValue")
    }
    
    logInfo(s"=== End $context Request Debug Info ===")
  }

  /**
   * Log Spark configuration relevant to proxy environments
   */
  def logSparkProxyConfig(sc: SparkContext): Unit = {
    logInfo("=== Spark Proxy Configuration ===")
    logInfo(s"Application ID: ${sc.applicationId}")
    logInfo(s"Application Name: ${sc.appName}")
    logInfo(s"Master: ${sc.master}")
    logInfo(s"Deploy Mode: ${sc.deployMode}")
    
    // Log relevant configuration keys
    val proxyRelatedKeys = Seq(
      "spark.master",
      "spark.yarn.historyServer.address",
      "spark.yarn.proxy.base",
      "spark.org.apache.hadoop.yarn.server.webproxy.amfilter.AmIpFilter.param.PROXY_URI_BASES",
      "spark.databricks.clusterUsageTags.cloudProvider",
      "spark.databricks.workspaceUrl",
      "spark.databricks.clusterUsageTags.clusterId",
      "spark.dataflint.proxy.baseUrl",
      "spark.dataflint.webUrl",
      "spark.ui.port",
      "spark.driver.host",
      "spark.driver.port"
    )
    
    proxyRelatedKeys.foreach { key =>
      sc.getConf.getOption(key) match {
        case Some(value) => logInfo(s"Config $key: $value")
        case None => logInfo(s"Config $key: <not set>")
      }
    }
    
    // Log environment variables
    logInfo("=== Relevant Environment Variables ===")
    val envVars = Seq(
      "YARN_WEB_PROXY_BASE",
      "EMR_CLUSTER_ID",
      "AWS_EMR_CLUSTER_ID",
      "EMR_MASTER_PUBLIC_DNS"
    )
    
    envVars.foreach { envVar =>
      Option(System.getenv(envVar)) match {
        case Some(value) => logInfo(s"Env $envVar: $value")
        case None => logInfo(s"Env $envVar: <not set>")
      }
    }
    
    logInfo("=== End Spark Proxy Configuration ===")
  }

  /**
   * Test API endpoint accessibility
   */
  def testApiEndpoint(baseUrl: String, endpoint: String): Boolean = {
    try {
      val url = s"$baseUrl/$endpoint"
      logInfo(s"Testing API endpoint: $url")
      
      // Simple connectivity test (this would need actual HTTP client in real implementation)
      // For now, just log the attempt
      logInfo(s"Would test connectivity to: $url")
      true
    } catch {
      case e: Exception =>
        logError(s"Failed to test API endpoint $baseUrl/$endpoint", e)
        false
    }
  }

  /**
   * Generate diagnostic report
   */
  def generateDiagnosticReport(sc: SparkContext, request: Option[HttpServletRequest] = None): String = {
    val report = new StringBuilder()
    
    report.append("=== DataFlint Proxy Diagnostic Report ===\n")
    report.append(s"Timestamp: ${java.time.Instant.now()}\n")
    report.append(s"Spark Version: ${sc.version}\n")
    report.append(s"Application ID: ${sc.applicationId}\n")
    report.append(s"Master: ${sc.master}\n")
    
    // Add proxy detection results
    import org.apache.spark.dataflint.config.ProxyEnvironmentDetector
    val proxyConfig = ProxyEnvironmentDetector.detectEnvironment(sc)
    report.append(s"Proxy Environment: ${proxyConfig.proxyType}\n")
    report.append(s"Is Proxy: ${proxyConfig.isProxyEnvironment}\n")
    proxyConfig.baseUrl.foreach { url =>
      report.append(s"Proxy Base URL: $url\n")
    }
    
    // Add request details if available
    request.foreach { req =>
      report.append(s"Request URI: ${req.getRequestURI}\n")
      report.append(s"Request URL: ${req.getRequestURL}\n")
    }
    
    report.append("=== End Diagnostic Report ===\n")
    report.toString()
  }
}
