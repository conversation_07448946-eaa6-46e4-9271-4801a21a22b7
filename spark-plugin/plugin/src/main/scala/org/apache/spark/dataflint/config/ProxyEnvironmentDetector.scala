package org.apache.spark.dataflint.config

import org.apache.spark.SparkContext
import org.apache.spark.internal.Logging

/**
 * Utility class to detect and handle proxy environments like EMR, Databricks, etc.
 */
object ProxyEnvironmentDetector extends Logging {

  case class ProxyConfig(
    isProxyEnvironment: Boolean,
    baseUrl: Option[String],
    proxyType: String,
    additionalHeaders: Map[String, String] = Map.empty
  )

  /**
   * Detect the proxy environment and return appropriate configuration
   */
  def detectEnvironment(sc: SparkContext): ProxyConfig = {
    val sparkMaster = sc.getConf.get("spark.master", "")
    val appId = sc.applicationId
    
    // Check for EMR environment
    if (isEMREnvironment(sc)) {
      logInfo("Detected EMR environment")
      val emrConfig = detectEMRConfig(sc, appId)
      return emrConfig
    }
    
    // Check for Databricks environment
    if (isDatabricksEnvironment(sc)) {
      logInfo("Detected Databricks environment")
      val databricksConfig = detectDatabricksConfig(sc, appId)
      return databricksConfig
    }
    
    // Check for generic YARN proxy environment
    if (isYarnProxyEnvironment(sc)) {
      logInfo("Detected YARN proxy environment")
      val yarnConfig = detectYarnProxyConfig(sc, appId)
      return yarnConfig
    }
    
    // Check for custom proxy configuration
    val customProxyUrl = sc.getConf.getOption("spark.dataflint.proxy.baseUrl")
    if (customProxyUrl.isDefined) {
      logInfo(s"Using custom proxy configuration: ${customProxyUrl.get}")
      return ProxyConfig(
        isProxyEnvironment = true,
        baseUrl = customProxyUrl,
        proxyType = "custom"
      )
    }
    
    // Default: no proxy
    logInfo("No proxy environment detected, using direct access")
    ProxyConfig(
      isProxyEnvironment = false,
      baseUrl = None,
      proxyType = "direct"
    )
  }

  private def isEMREnvironment(sc: SparkContext): Boolean = {
    // Check for EMR-specific configurations or environment variables
    val emrIndicators = Seq(
      sc.getConf.getOption("spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_TYPE"),
      sc.getConf.getOption("spark.executorEnv.YARN_CONTAINER_RUNTIME_TYPE"),
      Option(System.getenv("EMR_CLUSTER_ID")),
      Option(System.getenv("AWS_EMR_CLUSTER_ID"))
    )
    
    emrIndicators.exists(_.isDefined) || 
    sc.getConf.get("spark.master", "").contains("yarn") &&
    (sc.getConf.getAll.exists(_._1.contains("emr")) || 
     sc.getConf.getAll.exists(_._1.contains("aws")))
  }

  private def isDatabricksEnvironment(sc: SparkContext): Boolean = {
    sc.getConf.getOption("spark.databricks.clusterUsageTags.cloudProvider").isDefined
  }

  private def isYarnProxyEnvironment(sc: SparkContext): Boolean = {
    val sparkMaster = sc.getConf.get("spark.master", "")
    val hasYarnProxy = sc.getConf.getOption("spark.yarn.historyServer.address").isDefined ||
                      sc.getConf.getOption("spark.yarn.proxy.base").isDefined ||
                      Option(System.getenv("YARN_WEB_PROXY_BASE")).isDefined
    
    sparkMaster.startsWith("yarn") && hasYarnProxy
  }

  private def detectEMRConfig(sc: SparkContext, appId: String): ProxyConfig = {
    // Try to construct EMR proxy URL
    val emrClusterId = Option(System.getenv("EMR_CLUSTER_ID"))
      .orElse(Option(System.getenv("AWS_EMR_CLUSTER_ID")))
    
    val emrMasterPublicDns = Option(System.getenv("EMR_MASTER_PUBLIC_DNS"))
    val emrProxyBase = sc.getConf.getOption("spark.emr.proxy.base")
    
    val baseUrl = emrProxyBase.orElse {
      emrMasterPublicDns.map { dns =>
        s"https://$dns/gateway/emr/yarn/proxy/$appId"
      }
    }
    
    ProxyConfig(
      isProxyEnvironment = true,
      baseUrl = baseUrl,
      proxyType = "emr",
      additionalHeaders = Map(
        "X-Forwarded-Proto" -> "https",
        "X-Forwarded-Host" -> emrMasterPublicDns.getOrElse("unknown")
      )
    )
  }

  private def detectDatabricksConfig(sc: SparkContext, appId: String): ProxyConfig = {
    val databricksHost = sc.getConf.getOption("spark.databricks.workspaceUrl")
    val clusterId = sc.getConf.getOption("spark.databricks.clusterUsageTags.clusterId")
    
    val baseUrl = databricksHost.map { host =>
      s"https://$host/driver-proxy/o/0/$clusterId/4040"
    }
    
    ProxyConfig(
      isProxyEnvironment = true,
      baseUrl = baseUrl,
      proxyType = "databricks"
    )
  }

  private def detectYarnProxyConfig(sc: SparkContext, appId: String): ProxyConfig = {
    val yarnProxyBase = sc.getConf.getOption("spark.yarn.historyServer.address")
      .orElse(sc.getConf.getOption("spark.yarn.proxy.base"))
      .orElse(Option(System.getenv("YARN_WEB_PROXY_BASE")))
    
    val baseUrl = yarnProxyBase.map { base =>
      val cleanBase = if (base.endsWith("/")) base.dropRight(1) else base
      s"$cleanBase/proxy/$appId"
    }
    
    ProxyConfig(
      isProxyEnvironment = true,
      baseUrl = baseUrl,
      proxyType = "yarn"
    )
  }

  /**
   * Get the full DataFlint URL for the detected environment
   */
  def getDataFlintUrl(sc: SparkContext, defaultWebUrl: String): String = {
    val config = detectEnvironment(sc)
    
    config.baseUrl match {
      case Some(base) =>
        val cleanBase = if (base.endsWith("/")) base.dropRight(1) else base
        s"$cleanBase/dataflint"
      case None =>
        s"$defaultWebUrl/dataflint"
    }
  }
}
