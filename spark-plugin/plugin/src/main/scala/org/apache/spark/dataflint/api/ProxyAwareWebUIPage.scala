package org.apache.spark.dataflint.api

import org.apache.spark.internal.Logging
import org.apache.spark.ui.{SparkUI, WebUIPage}
import org.json4s.JValue

import javax.servlet.http.HttpServletRequest

/**
 * A proxy-aware WebUIPage that can handle requests in both direct and proxy environments
 */
abstract class ProxyAwareWebUIPage(prefix: String) extends WebUIPage(prefix) with Logging {

  /**
   * Enhanced renderJson method that handles proxy environments
   */
  override def renderJson(request: HttpServletRequest): JValue = {
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Processing JSON request")
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Request URI: ${request.getRequestURI()}")
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Request URL: ${request.getRequestURL()}")
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Context Path: ${request.getContextPath()}")
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Servlet Path: ${request.getServletPath()}")
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Path Info: ${request.getPathInfo()}")
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Query String: ${request.getQueryString()}")
    
    // Log headers for debugging proxy issues
    val headerNames = request.getHeaderNames()
    while (headerNames.hasMoreElements()) {
      val headerName = headerNames.nextElement()
      val headerValue = request.getHeader(headerName)
      logInfo(s"ProxyAwareWebUIPage[$prefix]: Header $headerName: $headerValue")
    }
    
    try {
      renderJsonInternal(request)
    } catch {
      case e: Exception =>
        logError(s"ProxyAwareWebUIPage[$prefix]: Error rendering JSON", e)
        throw e
    }
  }

  /**
   * Subclasses should implement this method instead of renderJson
   */
  protected def renderJsonInternal(request: HttpServletRequest): JValue

  /**
   * Helper method to detect if the request is coming through a proxy
   */
  protected def isProxyRequest(request: HttpServletRequest): Boolean = {
    val uri = request.getRequestURI()
    val contextPath = request.getContextPath()
    val servletPath = request.getServletPath()
    
    // Check for common proxy patterns
    val isProxy = uri.contains("/proxy/") || 
                  uri.contains("/gateway/") ||
                  contextPath.contains("/proxy/") ||
                  contextPath.contains("/gateway/") ||
                  Option(request.getHeader("X-Forwarded-For")).isDefined ||
                  Option(request.getHeader("X-Forwarded-Proto")).isDefined ||
                  Option(request.getHeader("X-Real-IP")).isDefined
    
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Proxy request detected: $isProxy")
    isProxy
  }

  /**
   * Helper method to get the base URL for API requests
   */
  protected def getBaseUrl(request: HttpServletRequest): String = {
    val scheme = Option(request.getHeader("X-Forwarded-Proto")).getOrElse(request.getScheme())
    val serverName = Option(request.getHeader("X-Forwarded-Host")).getOrElse(request.getServerName())
    val serverPort = request.getServerPort()
    val contextPath = request.getContextPath()
    
    val port = if ((scheme == "http" && serverPort == 80) || (scheme == "https" && serverPort == 443)) {
      ""
    } else {
      s":$serverPort"
    }
    
    val baseUrl = s"$scheme://$serverName$port$contextPath"
    logInfo(s"ProxyAwareWebUIPage[$prefix]: Base URL: $baseUrl")
    baseUrl
  }
}
