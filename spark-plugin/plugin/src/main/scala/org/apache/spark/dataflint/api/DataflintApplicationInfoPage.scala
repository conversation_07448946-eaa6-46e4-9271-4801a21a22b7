package org.apache.spark.dataflint.api

import org.apache.spark.dataflint.listener.DataflintStore
import org.apache.spark.dataflint.debug.ProxyDebugUtils
import org.apache.spark.internal.Logging
import org.apache.spark.ui.{SparkUI, WebUIPage}
import org.json4s.{Extraction, JObject, JValue}

import javax.servlet.http.HttpServletRequest
import scala.xml.Node

class DataflintApplicationInfoPage(ui: SparkUI, dataflintStore: DataflintStore)
  extends ProxyAwareWebUIPage("applicationinfo") {
  override def renderJsonInternal(request: HttpServletRequest): JValue = {
    try {
      // Enhanced debugging for proxy environments
      ProxyDebugUtils.logRequestDetails(request, "DataflintApplicationInfoPage")

      val runIdConfigFromStore = ui.store.environmentInfo().sparkProperties.find(_._1 == "spark.dataflint.runId").map(_._2)
      val runIdPotentiallyFromConfig = if (runIdConfigFromStore.isEmpty) ui.conf.getOption("spark.dataflint.runId") else runIdConfigFromStore

      logInfo(s"DataflintApplicationInfoPage: Getting application info from store")
      val applicationInfo = ui.store.applicationInfo()
      logInfo(s"DataflintApplicationInfoPage: Application info retrieved: ${applicationInfo.id}")

      logInfo(s"DataflintApplicationInfoPage: Getting environment info from dataflint store")
      val environmentInfo = dataflintStore.environmentInfo()
      logInfo(s"DataflintApplicationInfoPage: Environment info retrieved: ${environmentInfo.isDefined}")

      val dataFlintApplicationInfo = DataFlintApplicationInfo(runIdPotentiallyFromConfig, applicationInfo, environmentInfo)
      val jsonValue = Extraction.decompose(dataFlintApplicationInfo)(org.json4s.DefaultFormats)

      logInfo(s"DataflintApplicationInfoPage: Successfully generated JSON response with ${jsonValue.toString.length} characters")
      jsonValue
    }
    catch {
      case e: Throwable => {
        logError("failed to serve dataflint application info", e)
        logError(s"DataflintApplicationInfoPage: Exception details - ${e.getClass.getName}: ${e.getMessage}")
        if (e.getCause != null) {
          logError(s"DataflintApplicationInfoPage: Caused by - ${e.getCause.getClass.getName}: ${e.getCause.getMessage}")
        }
        // Return empty object but log the issue
        logError("DataflintApplicationInfoPage: Returning empty JSON object due to error")
        JObject()
      }
    }
  }

  override def render(request: HttpServletRequest): Seq[Node] = Seq[Node]()
}